<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserPotion;
use App\Models\PotionBeltSlot;
use App\Models\Potion;

/**
 * Команда для создания тестовых данных для зелий и пояса
 */
class CreateTestPotionData extends Command
{
    protected $signature = 'test:create-potion-data 
                            {user_id=10 : ID пользователя}';

    protected $description = 'Создание тестовых данных для зелий и пояса';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $this->info("🧪 Создание тестовых данных для зелий");
        $this->line("👤 Пользователь ID: {$userId}");
        
        // Находим пользователя
        $user = User::find($userId);
        if (!$user) {
            $this->error("❌ Пользователь с ID {$userId} не найден");
            
            // Показываем доступных пользователей
            $users = User::select('id', 'name')->take(5)->get();
            if ($users->isNotEmpty()) {
                $this->line("\n📋 Доступные пользователи:");
                foreach ($users as $u) {
                    $this->line("   ID {$u->id}: {$u->name}");
                }
            }
            return 1;
        }
        
        $this->line("✅ Пользователь найден: {$user->name}");
        
        // Находим базовые зелья в системе
        $potions = Potion::take(3)->get();
        if ($potions->isEmpty()) {
            $this->error("❌ В системе нет базовых зелий");
            return 1;
        }
        
        $this->line("🧪 Найдено базовых зелий: {$potions->count()}");
        
        // Создаем пользовательские зелья
        $createdPotions = collect();
        foreach ($potions as $potion) {
            $userPotion = UserPotion::create([
                'user_id' => $userId,
                'name' => $potion->name,
                'description' => $potion->description,
                'effect' => $potion->effect,
                'effect_value' => $potion->effect_value,
                'icon' => $potion->icon,
                'icon_path' => $potion->icon_path,
                'quality' => 'Обычное',
                'uses_left' => 5,
                'max_uses' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $createdPotions->push($userPotion);
            $this->line("   ✅ Создано зелье: {$userPotion->name} (ID: {$userPotion->id})");
        }
        
        // Добавляем зелья на пояс
        $this->line("\n🎒 Добавление зелий на пояс:");
        
        // Очищаем существующие слоты пояса
        PotionBeltSlot::where('user_id', $userId)->delete();
        
        $slotPosition = 1;
        foreach ($createdPotions as $userPotion) {
            if ($slotPosition <= 5) {
                PotionBeltSlot::create([
                    'user_id' => $userId,
                    'potion_id' => $userPotion->id,
                    'slot_position' => $slotPosition,
                ]);
                
                $this->line("   ✅ Слот {$slotPosition}: {$userPotion->name}");
                $slotPosition++;
            }
        }
        
        $this->info("\n🎉 Тестовые данные созданы успешно!");
        $this->line("📋 Создано:");
        $this->line("   • Зелий в инвентаре: {$createdPotions->count()}");
        $this->line("   • Слотов на поясе: " . ($slotPosition - 1));
        
        $this->line("\n🌐 Теперь можно тестировать:");
        $this->line("   1. Войдите в игру под пользователем {$user->name}");
        $this->line("   2. Перейдите на страницу боя (например, /battle/outposts/elven-haven)");
        $this->line("   3. Нажмите на зелье в панели быстрого доступа");
        $this->line("   4. Проверьте, что отображается флеш-сообщение вместо JS-уведомления");

        return 0;
    }
}
