<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Рудники - Echoes of Eternity</title>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/css/battle/mines-selection.css', 'resources/css/battle/mines-filters.css', 'resources/js/app.js', 'resources/js/battle/mines-selection.js', 'resources/js/battle/mines-filters.js', 'resources/js/battle/mines-dropdown-pagination.js']); ?>
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        
        <?php if (isset($component)) { $__componentOriginalee64098a97531effaa5ca39da6b3f2bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.hp-mp-bar','data' => ['actualResources' => $actualResources,'userProfile' => $userProfile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.hp-mp-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['actualResources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($actualResources),'userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile)]); ?>
            
            <?php if (isset($component)) { $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.notifications-bar','data' => ['hasUnreadMessages' => $hasUnreadMessages ?? false,'unreadMessagesCount' => $unreadMessagesCount ?? 0,'hasBrokenItems' => $hasBrokenItems ?? false,'brokenItemsCount' => $brokenItemsCount ?? 0]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.notifications-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['hasUnreadMessages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasUnreadMessages ?? false),'unreadMessagesCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($unreadMessagesCount ?? 0),'hasBrokenItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasBrokenItems ?? false),'brokenItemsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($brokenItemsCount ?? 0)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $attributes = $__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__attributesOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb)): ?>
<?php $component = $__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb; ?>
<?php unset($__componentOriginale4d3e407db3a46bc862a5907d9d4d4eb); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $attributes = $__attributesOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__attributesOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd)): ?>
<?php $component = $__componentOriginalee64098a97531effaa5ca39da6b3f2bd; ?>
<?php unset($__componentOriginalee64098a97531effaa5ca39da6b3f2bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal85b5c6510a4667d62a225031f53f7a0d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.currency-display','data' => ['userProfile' => $userProfile,'experienceProgress' => $experienceProgress ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.currency-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['userProfile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($userProfile),'experienceProgress' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($experienceProgress ?? null)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $attributes = $__attributesOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__attributesOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d)): ?>
<?php $component = $__componentOriginal85b5c6510a4667d62a225031f53f7a0d; ?>
<?php unset($__componentOriginal85b5c6510a4667d62a225031f53f7a0d); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal330dedb9455611dafc44712602451f6f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal330dedb9455611dafc44712602451f6f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.welcome-message','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.welcome-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $attributes = $__attributesOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__attributesOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal330dedb9455611dafc44712602451f6f)): ?>
<?php $component = $__componentOriginal330dedb9455611dafc44712602451f6f; ?>
<?php unset($__componentOriginal330dedb9455611dafc44712602451f6f); ?>
<?php endif; ?>

        
        <div class="mb-2">
            <?php if(isset($error)): ?>
                <div class="bg-[#613f36] text-[#ffeac1] p-1 rounded border border-[#88634a] shadow-md mb-3">
                    <?php echo e($error); ?>

                </div>
            <?php endif; ?>

            
            <?php if (isset($component)) { $__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.mines-header','data' => ['breadcrumbs' => $breadcrumbs,'title' => 'Рудники']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.mines-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs),'title' => 'Рудники']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce)): ?>
<?php $attributes = $__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce; ?>
<?php unset($__attributesOriginal1cd0b32bf85b74bb4544df7dc59961ce); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce)): ?>
<?php $component = $__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce; ?>
<?php unset($__componentOriginal1cd0b32bf85b74bb4544df7dc59961ce); ?>
<?php endif; ?>
        </div>

        
        <?php if (isset($component)) { $__componentOriginal1ec962ff0fee24a2d8516bedc6de20bd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1ec962ff0fee24a2d8516bedc6de20bd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.filter-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.filter-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1ec962ff0fee24a2d8516bedc6de20bd)): ?>
<?php $attributes = $__attributesOriginal1ec962ff0fee24a2d8516bedc6de20bd; ?>
<?php unset($__attributesOriginal1ec962ff0fee24a2d8516bedc6de20bd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1ec962ff0fee24a2d8516bedc6de20bd)): ?>
<?php $component = $__componentOriginal1ec962ff0fee24a2d8516bedc6de20bd; ?>
<?php unset($__componentOriginal1ec962ff0fee24a2d8516bedc6de20bd); ?>
<?php endif; ?>

        
        <?php if (isset($component)) { $__componentOriginal941e72106d31b03d9fd18fb13183e14e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal941e72106d31b03d9fd18fb13183e14e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.resource-filter-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.resource-filter-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal941e72106d31b03d9fd18fb13183e14e)): ?>
<?php $attributes = $__attributesOriginal941e72106d31b03d9fd18fb13183e14e; ?>
<?php unset($__attributesOriginal941e72106d31b03d9fd18fb13183e14e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal941e72106d31b03d9fd18fb13183e14e)): ?>
<?php $component = $__componentOriginal941e72106d31b03d9fd18fb13183e14e; ?>
<?php unset($__componentOriginal941e72106d31b03d9fd18fb13183e14e); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginald7230e11d497248904da8ada397e20a3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald7230e11d497248904da8ada397e20a3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.item-filter-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.item-filter-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald7230e11d497248904da8ada397e20a3)): ?>
<?php $attributes = $__attributesOriginald7230e11d497248904da8ada397e20a3; ?>
<?php unset($__attributesOriginald7230e11d497248904da8ada397e20a3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald7230e11d497248904da8ada397e20a3)): ?>
<?php $component = $__componentOriginald7230e11d497248904da8ada397e20a3; ?>
<?php unset($__componentOriginald7230e11d497248904da8ada397e20a3); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal7d641257fe2d4cf17a286a158de4c4df = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7d641257fe2d4cf17a286a158de4c4df = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.filter-notification-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.filter-notification-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7d641257fe2d4cf17a286a158de4c4df)): ?>
<?php $attributes = $__attributesOriginal7d641257fe2d4cf17a286a158de4c4df; ?>
<?php unset($__attributesOriginal7d641257fe2d4cf17a286a158de4c4df); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7d641257fe2d4cf17a286a158de4c4df)): ?>
<?php $component = $__componentOriginal7d641257fe2d4cf17a286a158de4c4df; ?>
<?php unset($__componentOriginal7d641257fe2d4cf17a286a158de4c4df); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginal4d955031482ac7876374c2c4993b19bb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4d955031482ac7876374c2c4993b19bb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.error-modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.error-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4d955031482ac7876374c2c4993b19bb)): ?>
<?php $attributes = $__attributesOriginal4d955031482ac7876374c2c4993b19bb; ?>
<?php unset($__attributesOriginal4d955031482ac7876374c2c4993b19bb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4d955031482ac7876374c2c4993b19bb)): ?>
<?php $component = $__componentOriginal4d955031482ac7876374c2c4993b19bb; ?>
<?php unset($__componentOriginal4d955031482ac7876374c2c4993b19bb); ?>
<?php endif; ?>

        
        <div class="mt-2 space-y-3" id="mines-list">
            
            <?php $__currentLoopData = $minesPaginator; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $mine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($mine->is_custom ?? false): ?>
                        <div class="bg-[#3b3a33] rounded-lg border border-[#a6925e] shadow-deep glow-border">
                            
                            <button onclick="toggleDetails('details-custom_mine_<?php echo e($index); ?>')"
                                class="w-full text-left text-[#e5b769] font-bold tracking-wide
                                                                                                                                                                                                              bg-gradient-to-b from-[#5a4d36] to-[#4a4a3d]
                                                                                                                                                                                                              border border-[#8b7548] shadow-md
                                                                                                                                                                                                              flex items-center justify-between
                                                                                                                                                                                                              rounded-lg overflow-hidden box-border">
                                <span class="flex items-center gap-2">
                                    <?php echo e($mine->name); ?>

                                </span>
                                <span class="text-lg transition-transform duration-200" id="icon-custom_mine_<?php echo e($index); ?>">▼</span>
                            </button>

                            
                            <div id="details-custom_mine_<?php echo e($index); ?>" class="hidden bg-[#2f2d2b] border-t border-[#8b7548] p-4">
                                
                                <div class="w-full bg-[#3b3a33] rounded-lg shadow-lg overflow-hidden mb-4">
                                    <img src="<?php echo e(asset($mine->image)); ?>" alt="Изображение локации" class="w-full h-24 opacity-90">
                                </div>

                                
                                <div class="mb-4">
                                    <?php if (isset($component)) { $__componentOriginalb22308892d0e76a700f735dedd90d9f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb22308892d0e76a700f735dedd90d9f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.faction-status','data' => ['solWarriors' => $solWarriors,'solMages' => $solMages,'solKnights' => $solKnights,'lunWarriors' => $lunWarriors,'lunMages' => $lunMages,'lunKnights' => $lunKnights]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.faction-status'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['solWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solWarriors),'solMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solMages),'solKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($solKnights),'lunWarriors' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunWarriors),'lunMages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunMages),'lunKnights' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($lunKnights)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $attributes = $__attributesOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__attributesOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb22308892d0e76a700f735dedd90d9f6)): ?>
<?php $component = $__componentOriginalb22308892d0e76a700f735dedd90d9f6; ?>
<?php unset($__componentOriginalb22308892d0e76a700f735dedd90d9f6); ?>
<?php endif; ?>
                                </div>





                            
                            <?php if (isset($component)) { $__componentOriginal69b712f723cc60259a203e1eaf31b9a0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal69b712f723cc60259a203e1eaf31b9a0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.battle.mines.mine-resources-dropdown','data' => ['mine' => $mine,'index' => $index,'resources' => $mine->resources ?? collect(),'possibleItems' => $mine->possible_items ?? collect(),'canEnterMines' => $canEnterMines]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('battle.mines.mine-resources-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['mine' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mine),'index' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($index),'resources' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mine->resources ?? collect()),'possibleItems' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($mine->possible_items ?? collect()),'canEnterMines' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($canEnterMines)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal69b712f723cc60259a203e1eaf31b9a0)): ?>
<?php $attributes = $__attributesOriginal69b712f723cc60259a203e1eaf31b9a0; ?>
<?php unset($__attributesOriginal69b712f723cc60259a203e1eaf31b9a0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal69b712f723cc60259a203e1eaf31b9a0)): ?>
<?php $component = $__componentOriginal69b712f723cc60259a203e1eaf31b9a0; ?>
<?php unset($__componentOriginal69b712f723cc60259a203e1eaf31b9a0); ?>
<?php endif; ?>


                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    
    <?php if(isset($minesPaginator)): ?>
        <?php if($minesPaginator->hasPages() || $minesPaginator->total() > 4): ?>
            <div class="mt-6 mb-4">
                <?php echo e($minesPaginator->links('vendor.pagination.simple-tailwind')); ?>

            </div>
        <?php endif; ?>
        
        
        <?php if(config('app.debug')): ?>
            <div class="mt-4 mb-2 text-center">
                <span class="text-[#9a9483] text-sm">
                    Рудников: <?php echo e($minesPaginator->total()); ?> | Страниц: <?php echo e($minesPaginator->lastPage()); ?> | hasPages: <?php echo e($minesPaginator->hasPages() ? 'Да' : 'Нет'); ?>

                </span>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    </div>

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/battle/mines.blade.php ENDPATH**/ ?>