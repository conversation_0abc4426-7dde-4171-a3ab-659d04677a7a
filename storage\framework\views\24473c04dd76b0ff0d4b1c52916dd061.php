<!DOCTYPE html>
<html lang="en">
<?php use Illuminate\Support\Facades\Auth; ?> 

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title>Персонаж: <?php echo e(Auth::check() ? Auth::user()->name : ($user->name ?? 'Гость')); ?></title>

    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>



<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    
    
    <div
        class="container max-w-md mx-auto px-1 py-0 bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden">

        
        
        <div class=""> 
            <div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner">
                
                <div class="flex items-center">
                    <div
                        class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] mr-1">
                        
                        <span class="text-[#FF6347] text-xs">❤️</span>
                    </div>
                    <div class="flex flex-col hp-container relative">
                        
                        <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                            <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full hp-bar"
                                style="width: calc(<?php echo e($actualResources['current_hp'] ?? 0); ?>/<?php echo e($userProfile->max_hp ?? 1); ?>*100%)">
                                
                            </div>
                        </div>
                        
                        <span
                            class="text-[#e5b769] text-[12px] hp-text"><?php echo e($actualResources['current_hp'] ?? 0); ?>/<?php echo e($userProfile->max_hp ?? '?'); ?></span>
                    </div>
                </div>

                
                
                <div class="flex space-x-1">
                    
                    <a href="<?php echo e(route('messages.index')); ?>" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <?php if(isset($hasUnreadMessages) && $hasUnreadMessages): ?>
                                <img src="<?php echo e(asset('assets/notification/message.png')); ?>" alt="Сообщения"
                                    class="w-5 h-5 animate-pulse filter hover:brightness-125 transition-all duration-300"
                                    style="animation: messagePulse 2s infinite;">
                            <?php else: ?>
                                <span class="block text-xs">📩</span>
                            <?php endif; ?>
                        </div>
                        <?php if(isset($hasUnreadMessages) && $hasUnreadMessages): ?>
                            <span
                                class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center animate-pulse">
                                <?php echo e($unreadMessagesCount > 9 ? '9+' : $unreadMessagesCount); ?>

                            </span>
                        <?php endif; ?>
                    </a>
                    
                    <a href="<?php echo e(route('bank.transactions.index')); ?>" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <?php if(isset($hasUnreadTransactions) && $hasUnreadTransactions): ?>
                                <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Транзакции"
                                    class="w-5 h-5 animate-pulse filter hover:brightness-125 transition-all duration-300"
                                    style="animation: messagePulse 2s infinite;">
                            <?php else: ?>
                                <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Транзакции" class="w-5 h-5">
                            <?php endif; ?>
                        </div>
                        <?php if(isset($hasUnreadTransactions) && $hasUnreadTransactions): ?>
                            <span
                                class="absolute -top-1 -right-1 bg-[#e74c3c] text-white text-[8px] rounded-full w-3 h-3 flex items-center justify-center animate-pulse">
                                <?php echo e($unreadTransactionsCount > 9 ? '9+' : $unreadTransactionsCount); ?>

                            </span>
                        <?php endif; ?>
                    </a>
                    
                    <a href="/events" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <span class="block text-xs">🔔</span>
                        </div>
                    </a>
                    
                    <a href="/quests" class="relative group">
                        <div
                            class="flex items-center justify-center w-6 h-6 bg-[#38352c] rounded-full border border-[#a6925e] hover:bg-[#4a452c] transition-colors duration-300">
                            <span class="block text-xs">📜</span>
                        </div>
                    </a>
                </div>

                
                <div class="flex items-center">
                    <div class="flex flex-col items-end">
                        
                        <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                            <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full"
                                style="width: calc(<?php echo e($userProfile->mp ?? 0); ?>/<?php echo e($userProfile->max_mp ?? 1); ?>*100%)">
                                
                            </div>
                        </div>
                        
                        <span
                            class="text-[#e5b769] text-[12px]"><?php echo e($userProfile->mp ?? 0); ?>/<?php echo e($userProfile->max_mp ?? '?'); ?></span>
                    </div>
                    <div
                        class="w-6 h-6 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e] ml-1">
                        
                        <span class="text-[#1E90FF] text-xs">🔮</span>
                    </div>
                </div>
            </div>
        </div>

        
        
        <div class="w-full bg-[#6b6658] mt-0.5 h-0.5 rounded-full mb-5"> 
            <?php
                // Используем те же переменные из блока опыта выше
                $experienceProgress = $experienceProgress ?? ['current_experience' => 0, 'next_experience' => 1, 'percentage' => 0];
                $percentage = $experienceProgress['percentage'];
            ?>
            
            <div class="bg-gradient-to-r from-[#a6925e] to-[#e5b769] h-full rounded-full transition-width duration-500"
                style="width: <?php echo e($percentage); ?>%;"></div>

            <div class="flex gap-1 items-center "> 
                
                <div class="flex items-center ">
                    <img src="<?php echo e(asset('assets/goldIcon.png')); ?>" alt="Золото" class="w-4 h-4"> 
                    <span class="text-sm font-medium text-[#e5b769]">
                        
                        <?php echo e(number_format($userProfile->gold ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/silverIcon.png')); ?>" alt="Серебро" class="w-4 h-4 mr-0.5"> 
                    <span class="text-sm font-medium text-[#c0c0c0]">
                        
                        <?php echo e(number_format($userProfile->silver ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
                
                <div class="flex items-center">
                    <img src="<?php echo e(asset('assets/bronzeIcon.png')); ?>" alt="Бронза" class="w-4 h-4 mr-0.5"> 
                    <span class="text-sm font-medium text-[#cd7f32]">
                        
                        <?php echo e(number_format($userProfile->bronze ?? 0, 0, ',', ' ')); ?>

                    </span>
                </div>
            </div>
        </div>


        
        
        <div class=""> 
            
            <?php if(isset($isDefeatState) && $isDefeatState): ?>
                
                <?php if (isset($component)) { $__componentOriginal3003adf5bd2f0f1cfa80504716d33784 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3003adf5bd2f0f1cfa80504716d33784 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.defeat-breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('defeat-breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3003adf5bd2f0f1cfa80504716d33784)): ?>
<?php $attributes = $__attributesOriginal3003adf5bd2f0f1cfa80504716d33784; ?>
<?php unset($__attributesOriginal3003adf5bd2f0f1cfa80504716d33784); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3003adf5bd2f0f1cfa80504716d33784)): ?>
<?php $component = $__componentOriginal3003adf5bd2f0f1cfa80504716d33784; ?>
<?php unset($__componentOriginal3003adf5bd2f0f1cfa80504716d33784); ?>
<?php endif; ?>
            <?php else: ?>
                
                <?php if (isset($component)) { $__componentOriginal360d002b1b676b6f84d43220f22129e2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal360d002b1b676b6f84d43220f22129e2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumbs','data' => ['breadcrumbs' => $breadcrumbs]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($breadcrumbs)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $attributes = $__attributesOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__attributesOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal360d002b1b676b6f84d43220f22129e2)): ?>
<?php $component = $__componentOriginal360d002b1b676b6f84d43220f22129e2; ?>
<?php unset($__componentOriginal360d002b1b676b6f84d43220f22129e2); ?>
<?php endif; ?>
            <?php endif; ?>

            
            <?php if(isset($canReturnToBattle) && $canReturnToBattle && isset($lastBattleRoute) && $lastBattleRoute): ?>
                
                <a href="<?php echo e(url($lastBattleRoute)); ?>" 
                    class="block w-full mb-2 max-w-xs mx-auto px-2 py-2 mt-3 text-lg font-semibold text-[#c4a46b]
                                                                                          bg-gradient-to-b from-[#2b2a21] to-[#1d1c17] border border-[#8c784e]
                                                                                          rounded-md uppercase tracking-wide text-center
                                                                                          hover:border-[#b59e70] hover:text-[#dfc590] hover:shadow-md
                                                                                          transition-all duration-300 magic-glow">
                    

                    ⚔️ ВЕРНУТЬСЯ В БОЙ ⚔️
                </a>
            <?php endif; ?>


        </div>



        
        <div class="bg-[#211f1a]   shadow-inner">
            <?php if (isset($component)) { $__componentOriginal5b09c79149dfb771c232996af5f9dae4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5b09c79149dfb771c232996af5f9dae4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.flash-messages','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flash-messages'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5b09c79149dfb771c232996af5f9dae4)): ?>
<?php $attributes = $__attributesOriginal5b09c79149dfb771c232996af5f9dae4; ?>
<?php unset($__attributesOriginal5b09c79149dfb771c232996af5f9dae4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5b09c79149dfb771c232996af5f9dae4)): ?>
<?php $component = $__componentOriginal5b09c79149dfb771c232996af5f9dae4; ?>
<?php unset($__componentOriginal5b09c79149dfb771c232996af5f9dae4); ?>
<?php endif; ?>
            <?php echo $__env->yieldContent('content'); ?>

        </div>
    </div> 

    
    <?php if (isset($component)) { $__componentOriginal9e67914025cd6dd9a8c10d111e91463c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.navigation-buttons','data' => ['isDisabled' => isset($isDefeatState) && $isDefeatState]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.navigation-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['isDisabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(isset($isDefeatState) && $isDefeatState)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $attributes = $__attributesOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__attributesOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c)): ?>
<?php $component = $__componentOriginal9e67914025cd6dd9a8c10d111e91463c; ?>
<?php unset($__componentOriginal9e67914025cd6dd9a8c10d111e91463c); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal4766510e0268a7a5917e77b146281554 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4766510e0268a7a5917e77b146281554 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.footer','data' => ['onlineCount' => $onlineCount]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onlineCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($onlineCount)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $attributes = $__attributesOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__attributesOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4766510e0268a7a5917e77b146281554)): ?>
<?php $component = $__componentOriginal4766510e0268a7a5917e77b146281554; ?>
<?php unset($__componentOriginal4766510e0268a7a5917e77b146281554); ?>
<?php endif; ?>



    
    <style>
        @keyframes messagePulse {
            0% {
                filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
            }

            50% {
                filter: drop-shadow(0 0 5px rgba(231, 76, 60, 0.7));
            }

            100% {
                filter: drop-shadow(0 0 0 rgba(231, 76, 60, 0));
            }
        }

        @keyframes fadeUp {
            0% {
                opacity: 0;
                transform: translateY(0);
            }

            20% {
                opacity: 1;
            }

            80% {
                opacity: 1;
            }

            100% {
                opacity: 0;
                transform: translateY(-15px);
            }
        }

        .animate-fade-up {
            animation: fadeUp 2s ease-out forwards;
        }
    </style>
</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/sample.blade.php ENDPATH**/ ?>