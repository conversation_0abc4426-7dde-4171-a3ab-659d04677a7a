

<div id="filterNotificationModal" class="fixed inset-0 z-50 hidden" aria-labelledby="filterNotificationModal-title" role="dialog" aria-modal="true">
    
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity duration-300 ease-out opacity-100"
            id="filterNotificationModal-backdrop" style="backdrop-filter: blur(3px);"></div>

        
        <div class="relative bg-[#252117] rounded-lg border border-[#514b3c] text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out scale-100 opacity-100 w-full max-w-md flex flex-col"
            id="filterNotificationModal-content">

            
            <div class="bg-[#2a2721] px-4 py-3 border-b border-[#514b3c] flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg leading-6 font-medium text-[#d9d3b8]" id="filterNotificationModal-title">
                                Фильтры активны
                            </h3>
                        </div>
                    </div>
                    <button type="button" id="closeFilterNotificationButton"
                        class="bg-[#38352c] rounded-md p-2 inline-flex items-center justify-center text-[#9a9483] hover:text-[#d9d3b8] hover:bg-[#4a452c] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#e5b769] transition-colors duration-200">
                        <span class="sr-only">Закрыть</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            
            <div class="p-4">
                <div class="text-center">
                    
                    

                    
                    <h4 class="text-lg font-medium text-[#d9d3b8] mb-2">
                        У вас активен фильтр
                    </h4>
                    <p class="text-[#9a9483] text-sm mb-4">
                        Локации рудников отфильтрованы по выбранным критериям. 
                        Отображаются только подходящие рудники.
                    </p>

                    
                    <div id="activeFiltersInfo" class="bg-[#1a1814] rounded-md p-3 mb-4 text-left">
                        <h5 class="text-[#e5b769] font-medium text-sm mb-2">Активные фильтры:</h5>
                        <div id="activeFiltersDetails" class="space-y-1 text-xs text-[#d9d3b8]">
                            
                        </div>
                    </div>

                    
                    <div class="flex items-center justify-center mb-4">
                        <input type="checkbox" id="dontShowAgainCheckbox" 
                               class="h-4 w-4 text-[#e5b769] bg-[#1a1814] border-[#514b3c] rounded 
                                      focus:ring-[#e5b769] focus:ring-2">
                        <label for="dontShowAgainCheckbox" class="ml-2 text-sm text-[#9a9483]">
                            Не показывать это уведомление снова
                        </label>
                    </div>
                </div>
            </div>

            
            <div class="bg-[#2a2721] px-4 py-3 border-t border-[#514b3c] flex-shrink-0">
                <div class="flex justify-center gap-3">
                    <button type="button" id="resetFiltersFromNotificationBtn"
                        class="bg-[#613f36] text-[#ffeac1] py-2 px-4 rounded shadow-md 
                               hover:bg-[#714a41] transition duration-300">
                        Сбросить фильтры
                    </button>
                    <button type="button" id="continueWithFiltersBtn"
                        class="bg-[#e5b769] text-[#2f2d2b] py-2 px-4 rounded shadow-md font-medium
                               hover:bg-[#f0c674] transition duration-300">
                        Продолжить
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/filter-notification-modal.blade.php ENDPATH**/ ?>