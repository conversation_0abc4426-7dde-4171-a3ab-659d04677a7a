<?php
    // Определяем классы для разных типов сообщений
    $messageTypes = [
        'success' => [
            'bg' => 'bg-[#36513f]',
            'text' => 'text-[#c8ffdb]',
            'border' => 'border-[#4a7759]',
            'icon' => '✅',
        ],
        'error' => [
            'bg' => 'bg-[#613f36]',
            'text' => 'text-[#ffeac1]',
            'border' => 'border-[#88634a]',
            'icon' => '❌',
        ],
        'warning' => [
            'bg' => 'bg-[#5e553a]',
            'text' => 'text-[#ffe7bd]',
            'border' => 'border-[#7d7248]',
            'icon' => '⚠️',
        ],
        'info' => [
            'bg' => 'bg-[#3a4a5e]',
            'text' => 'text-[#c1dcff]',
            'border' => 'border-[#4a617d]',
            'icon' => 'ℹ️',
        ],
    ];
?>


<?php if(session('success')): ?>
    <div
        class="<?php echo e($messageTypes['success']['bg']); ?> <?php echo e($messageTypes['success']['text']); ?> p-1.5 mx-2 my-1.5 text-xs rounded border <?php echo e($messageTypes['success']['border']); ?> shadow-inner shadow-black/30 mb-2">
        <?php echo session('success'); ?>

    </div>
<?php endif; ?>


<?php if(session('error')): ?>
    <div
        class="<?php echo e($messageTypes['error']['bg']); ?> <?php echo e($messageTypes['error']['text']); ?> p-1.5 mx-2 my-1.5 text-xs rounded border <?php echo e($messageTypes['error']['border']); ?> shadow-inner shadow-black/30 mb-2">
        <?php echo session('error'); ?>

    </div>
<?php endif; ?>


<?php if(session('warning')): ?>
    <div
        class="<?php echo e($messageTypes['warning']['bg']); ?> <?php echo e($messageTypes['warning']['text']); ?> p-1.5 mx-2 my-1.5 text-xs rounded border <?php echo e($messageTypes['warning']['border']); ?> shadow-inner shadow-black/30 mb-2">
        <?php echo session('warning'); ?>

    </div>
<?php endif; ?>


<?php if(session('info')): ?>
    <div
        class="<?php echo e($messageTypes['info']['bg']); ?> <?php echo e($messageTypes['info']['text']); ?> p-1.5 mx-2 my-1.5 text-xs rounded border <?php echo e($messageTypes['info']['border']); ?> shadow-inner shadow-black/30 mb-2">
        <?php echo session('info'); ?>

    </div>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/flash-messages.blade.php ENDPATH**/ ?>