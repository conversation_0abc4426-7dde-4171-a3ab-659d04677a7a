

<div id="itemFilterModal" class="fixed inset-0 z-50 hidden" aria-labelledby="itemFilterModal-title" role="dialog"
    aria-modal="true">
    
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity duration-300 ease-out opacity-100"
            id="itemFilterModal-backdrop" style="backdrop-filter: blur(3px);"></div>

        
        <div class="relative bg-[#252117] rounded-lg border border-[#514b3c] text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out scale-100 opacity-100 w-full max-w-2xl flex flex-col"
            id="itemFilterModal-content" style="max-height: 90vh;">

            
            <div class="bg-[#2a2721] px-4 py-3 border-b border-[#514b3c] flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg leading-6 font-medium text-[#d9d3b8]" id="itemFilterModal-title">
                                Фильтр по предметам
                            </h3>
                            <p class="text-sm text-[#9a9483]">Выберите предметы для поиска рудников</p>
                        </div>
                    </div>
                    <button type="button" id="closeItemFilterButton"
                        class="bg-[#38352c] rounded-md p-2 inline-flex items-center justify-center text-[#9a9483] hover:text-[#d9d3b8] hover:bg-[#4a452c] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#e5b769] transition-colors duration-200">
                        <span class="sr-only">Закрыть</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            
            <div class="flex-1 overflow-y-auto p-4" id="itemFilterContent">
                
                <div class="mb-4">
                    <label for="itemSearch" class="block text-sm font-medium text-[#d9d3b8] mb-2">
                        Поиск по названию:
                    </label>
                    <input type="text" id="itemSearch" class="w-full bg-[#1a1814] border border-[#514b3c] rounded-md px-3 py-2 
                                  text-[#d9d3b8] placeholder-[#9a9483] 
                                  focus:outline-none focus:ring-2 focus:ring-[#e5b769] focus:border-transparent"
                        placeholder="Введите название предмета...">
                </div>

                
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    
                    <div>
                        <label for="itemTypeFilter" class="block text-sm font-medium text-[#d9d3b8] mb-2">
                            Тип предмета:
                        </label>
                        <div class="relative">
                            
                            <select id="itemTypeFilter" class="hidden">
                                <option value="">Все типы</option>
                            </select>

                            
                            <div class="custom-dropdown">
                                <button type="button" id="itemTypeDropdownButton"
                                    class="w-full bg-[#1a1814] border border-[#514b3c] rounded-md px-3 py-2
                                               text-[#d9d3b8] focus:outline-none focus:ring-2 focus:ring-[#e5b769] focus:border-transparent
                                               flex items-center justify-between cursor-pointer hover:border-[#e5b769] transition-colors">
                                    <span id="itemTypeDropdownText">Все типы</span>
                                    <svg class="w-4 h-4 transition-transform duration-200" id="itemTypeDropdownArrow"
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                
                                <div id="itemTypeDropdownList" class="absolute z-50 w-full mt-1 bg-[#1a1814] border border-[#514b3c] rounded-md shadow-lg
                                            max-h-[200px] overflow-y-auto hidden custom-scrollbar">
                                    <div class="py-1">
                                        <div class="dropdown-item px-3 py-2 text-[#d9d3b8] hover:bg-[#2a2721] cursor-pointer transition-colors"
                                            data-value="">
                                            Все типы
                                        </div>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <div>
                        <label for="itemQualityFilter" class="block text-sm font-medium text-[#d9d3b8] mb-2">
                            Качество:
                        </label>
                        <select id="itemQualityFilter"
                            class="w-full bg-[#1a1814] border border-[#514b3c] rounded-md px-3 py-2
                                       text-[#d9d3b8] focus:outline-none focus:ring-2 focus:ring-[#e5b769] focus:border-transparent">
                            <option value="">Любое качество</option>
                            <option value="Обычное">Обычное</option>
                            <option value="Необычное">Необычное</option>
                            <option value="Редкое">Редкое</option>
                            <option value="Эпическое">Эпическое</option>
                            <option value="Легендарное">Легендарное</option>
                        </select>
                    </div>
                </div>

                
                <div class="flex gap-2 mb-4">
                    <button id="selectAllItemsBtn" class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm
                                   hover:bg-[#4a452c] transition-colors duration-200">
                        Выбрать все
                    </button>
                    <button id="deselectAllItemsBtn" class="bg-[#613f36] text-[#ffeac1] px-3 py-1 rounded text-sm
                                   hover:bg-[#714a41] transition-colors duration-200">
                        Снять все
                    </button>
                    <button id="resetItemFiltersBtn" class="bg-[#3e342c] text-[#fceac4] px-3 py-1 rounded text-sm
                                   hover:bg-[#4a3f35] transition-colors duration-200">
                        Сбросить фильтры
                    </button>
                </div>

                
                <div id="itemsGrid" class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
                    
                    <div class="col-span-full flex justify-center items-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#e5b769]"></div>
                        <span class="ml-2 text-[#d9d3b8]">Загрузка предметов...</span>
                    </div>
                </div>

                
                <div id="itemsPagination" class="hidden mt-4 flex justify-center items-center gap-2">
                    <button id="prevItemsPage"
                        class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm 
                                   hover:bg-[#4a452c] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        ← Назад
                    </button>
                    <span id="itemsPageInfo" class="text-[#d9d3b8] text-sm px-3"></span>
                    <button id="nextItemsPage"
                        class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm 
                                   hover:bg-[#4a452c] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        Вперед →
                    </button>
                </div>
            </div>

            
            <div class="bg-[#2a2721] px-4 py-3 border-t border-[#514b3c] flex-shrink-0">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-[#9a9483]">
                        Выбрано: <span id="selectedItemsCount" class="text-[#e5b769] font-medium">0</span> предметов
                    </div>
                    <div class="flex gap-2">
                        <button type="button" id="cancelItemFilterButton" class="bg-[#514b3c] text-[#d9d3b8] py-2 px-4 rounded shadow-md 
                                   hover:bg-[#5d5745] transition duration-300">
                            Отмена
                        </button>
                        <button type="button" id="applyItemFilterButton" class="bg-[#e5b769] text-[#2f2d2b] py-2 px-4 rounded shadow-md font-medium
                                   hover:bg-[#f0c674] transition duration-300">
                            Применить фильтр
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/item-filter-modal.blade.php ENDPATH**/ ?>