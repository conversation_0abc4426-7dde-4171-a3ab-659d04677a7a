

<div id="resourceFilterModal" class="fixed inset-0 z-50 hidden" aria-labelledby="resourceFilterModal-title" role="dialog" aria-modal="true">
    
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity duration-300 ease-out opacity-100"
            id="resourceFilterModal-backdrop" style="backdrop-filter: blur(3px);"></div>

        
        <div class="relative bg-[#252117] rounded-lg border border-[#514b3c] text-left overflow-hidden shadow-xl transform transition-all duration-300 ease-out scale-100 opacity-100 w-full max-w-2xl flex flex-col"
            id="resourceFilterModal-content" style="max-height: 90vh;">

            
            <div class="bg-[#2a2721] px-4 py-3 border-b border-[#514b3c] flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg leading-6 font-medium text-[#d9d3b8]" id="resourceFilterModal-title">
                                Фильтр по ресурсам
                            </h3>
                            <p class="text-sm text-[#9a9483]">Выберите ресурсы для поиска рудников</p>
                        </div>
                    </div>
                    <button type="button" id="closeResourceFilterButton"
                        class="bg-[#38352c] rounded-md p-2 inline-flex items-center justify-center text-[#9a9483] hover:text-[#d9d3b8] hover:bg-[#4a452c] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#e5b769] transition-colors duration-200">
                        <span class="sr-only">Закрыть</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            
            <div class="flex-1 overflow-y-auto p-4" id="resourceFilterContent">
                
                <div class="mb-4">
                    <label for="resourceSearch" class="block text-sm font-medium text-[#d9d3b8] mb-2">
                        Поиск по названию:
                    </label>
                    <input type="text" id="resourceSearch" 
                           class="w-full bg-[#1a1814] border border-[#514b3c] rounded-md px-3 py-2 
                                  text-[#d9d3b8] placeholder-[#9a9483] 
                                  focus:outline-none focus:ring-2 focus:ring-[#e5b769] focus:border-transparent"
                           placeholder="Введите название ресурса...">
                </div>

                
                <div class="flex gap-2 mb-4">
                    <button id="selectAllResourcesBtn" 
                            class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm 
                                   hover:bg-[#4a452c] transition-colors duration-200">
                        Выбрать все
                    </button>
                    <button id="deselectAllResourcesBtn" 
                            class="bg-[#613f36] text-[#ffeac1] px-3 py-1 rounded text-sm 
                                   hover:bg-[#714a41] transition-colors duration-200">
                        Снять все
                    </button>
                </div>

                
                <div id="resourcesGrid" class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-3">
                    
                    <div class="col-span-full flex justify-center items-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#e5b769]"></div>
                        <span class="ml-2 text-[#d9d3b8]">Загрузка ресурсов...</span>
                    </div>
                </div>

                
                <div id="resourcesPagination" class="hidden mt-4 flex justify-center items-center gap-2">
                    <button id="prevResourcesPage" 
                            class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm 
                                   hover:bg-[#4a452c] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        ← Назад
                    </button>
                    <span id="resourcesPageInfo" class="text-[#d9d3b8] text-sm px-3"></span>
                    <button id="nextResourcesPage" 
                            class="bg-[#38352c] text-[#e5b769] px-3 py-1 rounded text-sm 
                                   hover:bg-[#4a452c] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        Вперед →
                    </button>
                </div>
            </div>

            
            <div class="bg-[#2a2721] px-4 py-3 border-t border-[#514b3c] flex-shrink-0">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-[#9a9483]">
                        Выбрано: <span id="selectedResourcesCount" class="text-[#e5b769] font-medium">0</span> ресурсов
                    </div>
                    <div class="flex gap-2">
                        <button type="button" id="cancelResourceFilterButton"
                            class="bg-[#514b3c] text-[#d9d3b8] py-2 px-4 rounded shadow-md 
                                   hover:bg-[#5d5745] transition duration-300">
                            Отмена
                        </button>
                        <button type="button" id="applyResourceFilterButton"
                            class="bg-[#e5b769] text-[#2f2d2b] py-2 px-4 rounded shadow-md font-medium
                                   hover:bg-[#f0c674] transition duration-300">
                            Применить фильтр
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/resource-filter-modal.blade.php ENDPATH**/ ?>