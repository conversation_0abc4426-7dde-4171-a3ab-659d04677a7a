<?php if($paginator->hasPages() || $paginator->total() > $paginator->perPage()): ?>
    <div class="mt-6 mb-4 flex justify-center items-center">
        
        <div class="relative">
            
            <div
                class="relative bg-gradient-to-b from-[#312e25] to-[#1a1814] rounded-lg px-2 py-2 sm:px-4 sm:py-3 md:px-5 md:py-3 
                    border border-[#a6925e] shadow-md">

                <div class="flex items-center justify-center space-x-1 sm:space-x-2">
                    
                    <?php if($paginator->onFirstPage()): ?>
                        <button disabled
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                    <?php else: ?>
                        <a href="<?php echo e($paginator->previousPageUrl()); ?>"
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 relative z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </a>
                    <?php endif; ?>

                    
                    <?php
                        $current = $paginator->currentPage();
                        $last = $paginator->lastPage();
                        
                        // Показываем текущую страницу + соседние + последнюю
                        $showPages = collect();
                        
                        // Всегда показываем первую страницу
                        $showPages->push(1);
                        
                        // Добавляем предыдущую страницу (если есть и она не 1)
                        if ($current > 1 && $current - 1 != 1) {
                            $showPages->push($current - 1);
                        }
                        
                        // Добавляем текущую страницу (если она не 1)
                        if ($current != 1) {
                            $showPages->push($current);
                        }
                        
                        // Добавляем следующую страницу (если есть и она не последняя)
                        if ($current < $last && $current + 1 != $last) {
                            $showPages->push($current + 1);
                        }
                        
                        // Добавляем последнюю страницу (если она не равна текущей или первой)
                        if ($last > 1 && $last != $current) {
                            $showPages->push($last);
                        }
                        
                        // Сортируем и убираем дубли
                        $showPages = $showPages->unique()->sort()->values();
                    ?>
                    
                    
                    <?php $__currentLoopData = $showPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        
                        <?php if($index > 0 && $page - $showPages->get($index - 1) > 1): ?>
                            <span class="w-6 h-8 sm:w-8 sm:h-10 flex items-center justify-center text-[#9a9483] text-xs sm:text-sm">...</span>
                        <?php endif; ?>
                        
                        
                        <?php if($page == $current): ?>
                            <span class="w-6 h-8 sm:w-8 sm:h-10 md:w-10 md:h-10 flex items-center justify-center rounded border border-[#c1a96e] bg-gradient-to-b from-[#e5b769] to-[#c4a76d] text-[#1a1814] shadow-lg font-bold text-xs sm:text-sm">
                                <?php echo e($page); ?>

                            </span>
                        <?php else: ?>
                            <a href="<?php echo e($paginator->url($page)); ?>"
                                class="w-6 h-8 sm:w-8 sm:h-10 md:w-10 md:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 text-xs sm:text-sm font-medium relative z-10">
                                <?php echo e($page); ?>

                            </a>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    
                    <?php if($paginator->hasMorePages()): ?>
                        <a href="<?php echo e($paginator->nextPageUrl()); ?>"
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#a6925e] bg-gradient-to-b from-[#38352c] to-[#28241c] text-[#e5b769] shadow-lg hover:from-[#413c33] hover:to-[#2f2b23] transition-all duration-300 relative z-10">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>
                    <?php else: ?>
                        <button disabled
                            class="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded border border-[#3d3a32] bg-[#1a1814] text-[#514b3c] cursor-not-allowed opacity-70">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <div class="text-center mb-4">
        <div class="inline-block px-2 py-1 sm:px-3 sm:py-1 bg-[#1a1814] rounded border-t border-b border-[#514b3c]">
            <span class="text-[#9a9483] text-xs sm:text-sm">Страница <?php echo e($paginator->currentPage()); ?> из
                <?php echo e($paginator->lastPage()); ?></span>
        </div>
        
        
        <?php if(config('app.debug')): ?>
            <div class="mt-2 text-xs text-[#9a9483] border border-[#514b3c] p-1 sm:p-2 rounded">
                <div>Всего: <?php echo e($paginator->total()); ?> | На странице: <?php echo e($paginator->perPage()); ?> | Показано: <?php echo e($paginator->count()); ?></div>
                <div>Текущая: <?php echo e($paginator->currentPage()); ?> | Последняя: <?php echo e($paginator->lastPage()); ?></div>
                <div>hasPages: <?php echo e($paginator->hasPages() ? 'Да' : 'Нет'); ?> | hasMorePages: <?php echo e($paginator->hasMorePages() ? 'Да' : 'Нет'); ?></div>
                <div>nextUrl: <?php echo e($paginator->nextPageUrl() ?? 'null'); ?></div>
                <div>Ожидаемых страниц: <?php echo e(ceil($paginator->total() / $paginator->perPage())); ?></div>
                <?php
                    $expectedNext = $paginator->currentPage() < $paginator->lastPage();
                ?>
                <div>Должна быть следующая: <?php echo e($expectedNext ? 'Да' : 'Нет'); ?></div>
                <div class="mt-2">
                    <div>Тест ссылки: <a href="<?php echo e($paginator->url(1)); ?>" class="text-[#e5b769] underline">Страница 1</a></div>
                    <?php if($paginator->hasMorePages()): ?>
                        <div>Тест следующей: <a href="<?php echo e($paginator->nextPageUrl()); ?>" class="text-[#e5b769] underline">Следующая</a></div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/vendor/pagination/simple-tailwind.blade.php ENDPATH**/ ?>