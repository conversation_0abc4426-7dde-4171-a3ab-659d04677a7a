

<div id="errorModal" class="fixed inset-0 z-[60] hidden" aria-labelledby="errorModal-title" role="dialog"
    aria-modal="true">
    
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-75 transition-opacity duration-300 ease-out opacity-100"
            style="backdrop-filter: blur(3px);"></div>

        
        <div class="error-modal-content relative bg-[#1a1814] rounded-lg shadow-xl border border-[#514b3c]
                    max-w-md w-full mx-auto transform transition-all duration-300 ease-out
                    opacity-0 scale-95">

            
            <div class="bg-[#2a2721] px-4 py-3 border-b border-[#514b3c] flex-shrink-0 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-[#e5b769] rounded-full flex items-center justify-center">
                                <span class="text-[#2f2d2b] font-bold text-lg">!</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg leading-6 font-medium text-[#d9d3b8]" id="errorModal-title">
                                Ошибка фильтрации
                            </h3>
                            <p class="text-sm text-[#9a9483]">Проверьте параметры фильтрации</p>
                        </div>
                    </div>
                    <button type="button" id="closeErrorModalButton" class="bg-transparent border-0 text-[#9a9483] hover:text-[#d9d3b8]
                               focus:outline-none focus:text-[#d9d3b8] transition duration-150 ease-in-out"
                        aria-label="Закрыть">
                        <span class="sr-only">Закрыть</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            
            <div class="p-6">
                <div class="text-center">


                    
                    <div class="mb-4">
                        <p id="errorMessage" class="text-[#d9d3b8] text-base leading-relaxed">
                            Произошла ошибка при обработке запроса
                        </p>
                    </div>

                    
                    <div class="mt-6">
                        <button type="button" onclick="mineFilters.closeErrorModal()" class="bg-[#613f36] text-[#ffeac1] py-2 px-4 rounded shadow-md
                                       hover:bg-[#714a41] transition-colors duration-200">
                            Понятно
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/error-modal.blade.php ENDPATH**/ ?>