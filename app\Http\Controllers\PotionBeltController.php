<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\UserPotion;
use App\Models\Potion;
use App\Models\PotionBeltSlot;
use App\Models\GameItem;
use App\Services\FlashMessageService;

class PotionBeltController extends Controller
{
    private FlashMessageService $flashMessageService;

    public function __construct(FlashMessageService $flashMessageService)
    {
        $this->flashMessageService = $flashMessageService;
    }
    /**
     * Добавить зелье на пояс
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addToBelt(Request $request)
    {
        // Валидация запроса
        $validated = $request->validate([
            'potion_id' => 'required|integer',
            'slot_position' => 'required|integer|min:1|max:5',
        ]);

        // Логируем запрос для отладки
        Log::info('Запрос на добавление зелья на пояс', [
            'potion_id' => $validated['potion_id'],
            'slot_position' => $validated['slot_position'],
            'user_id' => Auth::id()
        ]);

        $user = Auth::user();

        // Сначала пробуем найти зелье в таблице user_potions
        $potion = UserPotion::where('id', $validated['potion_id'])
            ->where('user_id', $user->id)
            ->where('location', UserPotion::LOCATION_INVENTORY)
            ->first();

        // Если не нашли в user_potions, ищем в таблице potions (старая версия)
        if (!$potion) {
            $oldPotion = Potion::where('id', $validated['potion_id'])
                ->where('user_id', $user->id)
                ->where('location', 'inventory')
                ->first();

            if ($oldPotion) {
                // Конвертируем старое зелье в новое
                $potion = UserPotion::createFromTemplate($oldPotion, $user->id, UserPotion::LOCATION_INVENTORY);

                // Удаляем старое зелье
                $oldPotion->delete();

                Log::info('Старое зелье конвертировано в новое для добавления на пояс', [
                    'user_id' => $user->id,
                    'old_potion_id' => $oldPotion->id,
                    'new_potion_id' => $potion->id
                ]);
            }
        }

        if (!$potion) {
            Log::warning('Попытка добавить на пояс зелье, которое не принадлежит пользователю или не находится в инвентаре', [
                'user_id' => $user->id,
                'potion_id' => $validated['potion_id']
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Зелье не найдено или не может быть добавлено на пояс.'
            ], 404);
        }

        // Проверяем, экипирован ли пояс
        if (!PotionBeltSlot::canUseBelt($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Вы должны экипировать пояс, чтобы использовать его.'
            ], 400);
        }

        // Проверяем, не занят ли уже этот слот
        $existingSlot = PotionBeltSlot::where('user_id', $user->id)
            ->where('slot_position', $validated['slot_position'])
            ->first();

        if ($existingSlot) {
            return response()->json([
                'success' => false,
                'message' => 'Этот слот на поясе уже занят.'
            ], 400);
        }

        // Перемещаем зелье на пояс
        $success = $potion->moveToBelt($validated['slot_position']);

        if ($success) {
            // Обновляем счетчик использованных слотов инвентаря
            $user->profile->decrement('inventory_used');

            return response()->json([
                'success' => true,
                'message' => 'Зелье успешно добавлено на пояс.',
                'inventory_used' => $user->profile->inventory_used,
                'inventory_capacity' => $user->profile->inventory_capacity
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Не удалось добавить зелье на пояс.'
            ], 500);
        }
    }

    /**
     * Удалить зелье с пояса
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeFromBelt(Request $request)
    {
        // Валидация запроса
        $validated = $request->validate([
            'slot_position' => 'required|integer|min:1|max:5',
        ]);

        $user = Auth::user();

        // Находим слот на поясе
        $beltSlot = PotionBeltSlot::where('user_id', $user->id)
            ->where('slot_position', $validated['slot_position'])
            ->first();

        if (!$beltSlot) {
            return response()->json([
                'success' => false,
                'message' => 'Слот на поясе не найден или пуст.'
            ], 404);
        }

        // Находим зелье
        $potion = UserPotion::find($beltSlot->potion_id);

        if (!$potion) {
            // Если зелье не найдено, просто удаляем запись о слоте
            $beltSlot->delete();
            return response()->json([
                'success' => true,
                'message' => 'Слот на поясе очищен.'
            ]);
        }

        // Проверяем, есть ли место в инвентаре
        if ($user->profile->inventory_used >= $user->profile->inventory_capacity) {
            return response()->json([
                'success' => false,
                'message' => 'В рюкзаке нет места для зелья.'
            ], 400);
        }

        // Перемещаем зелье в инвентарь
        $success = $potion->moveToInventory();

        if ($success) {
            // Обновляем счетчик использованных слотов инвентаря
            $user->profile->increment('inventory_used');

            return response()->json([
                'success' => true,
                'message' => 'Зелье успешно перемещено в рюкзак.',
                'inventory_used' => $user->profile->inventory_used,
                'inventory_capacity' => $user->profile->inventory_capacity
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Не удалось переместить зелье в рюкзак.'
            ], 500);
        }
    }

    /**
     * Использовать зелье с пояса
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function usePotionFromBelt(Request $request)
    {
        // Валидация запроса
        $validated = $request->validate([
            'slot_position' => 'required|integer|min:1|max:5',
        ]);

        $user = Auth::user();

        // Защита от спама использования зелий
        $limiterKey = "use-belt-potion:{$user->id}";
        $maxAttempts = 3; // Максимум 3 попытки
        $decaySeconds = 5; // За 5 секунд

        if (\Illuminate\Support\Facades\RateLimiter::tooManyAttempts($limiterKey, $maxAttempts)) {
            $seconds = \Illuminate\Support\Facades\RateLimiter::availableIn($limiterKey);
            $this->flashMessageService->warning("Слишком быстро! Подождите {$seconds} сек.");
            return response()->json([
                'success' => false,
                'reload' => true
            ], 429);
        }

        \Illuminate\Support\Facades\RateLimiter::hit($limiterKey, $decaySeconds);

        // Находим слот на поясе
        $beltSlot = PotionBeltSlot::where('user_id', $user->id)
            ->where('slot_position', $validated['slot_position'])
            ->first();

        if (!$beltSlot) {
            $this->flashMessageService->error('Слот на поясе не найден или пуст.');
            return response()->json([
                'success' => false,
                'reload' => true
            ], 404);
        }

        // Находим зелье
        $potion = UserPotion::find($beltSlot->potion_id);

        if (!$potion) {
            // Если зелье не найдено, просто удаляем запись о слоте
            $beltSlot->delete();
            $this->flashMessageService->error('Зелье не найдено.');
            return response()->json([
                'success' => false,
                'reload' => true
            ], 404);
        }

        // Сохраняем информацию о зелье перед применением
        $potionName = $potion->name;
        $potionEffect = $potion->effect;

        // Получаем текущие значения HP/MP перед применением зелья
        $beforeResources = $user->profile->getActualResources();

        // Применяем зелье к пользователю
        $success = $potion->applyToUser($user);

        if ($success) {
            // Получаем обновленные значения HP/MP после применения зелья
            $afterResources = $user->profile->getActualResources();

            // Проверяем, было ли зелье полностью использовано
            $potionRemoved = !UserPotion::find($beltSlot->potion_id);

            // Если зелье было удалено, то запись о слоте на поясе тоже должна быть удалена
            if ($potionRemoved) {
                $beltSlot->delete();
            }

            // Формируем сообщение об эффекте зелья
            $effectMessage = '';
            $hpDiff = 0;
            $mpDiff = 0;

            if (strpos($potionEffect, 'health') !== false || strpos($potionEffect, 'Восстановление HP') !== false) {
                $hpDiff = $afterResources['current_hp'] - $beforeResources['current_hp'];
                $effectMessage = "Восстановлено {$hpDiff} HP";
            } elseif (strpos($potionEffect, 'mana') !== false || strpos($potionEffect, 'Восстановление MP') !== false) {
                $mpDiff = $afterResources['current_mp'] - $beforeResources['current_mp'];
                $effectMessage = "Восстановлено {$mpDiff} MP";
            } else {
                $effectMessage = $potionEffect;
            }

            // Добавляем флеш-сообщение об успешном использовании зелья
            $message = "Вы использовали {$potionName}. {$effectMessage}";

            // Получаем иконку зелья для отображения в сообщении
            $potionIcon = $potion->icon_path ?? null;
            if ($potionIcon) {
                $iconHtml = '<img src="' . asset($potionIcon) . '" alt="' . $potionName . '" class="w-4 h-4 inline-block mr-1">';
                $this->flashMessageService->success($message, $iconHtml);
            } else {
                // Используем эмодзи зелья как иконку по умолчанию
                $this->flashMessageService->success($message, '🧪');
            }

            return response()->json([
                'success' => true,
                'reload' => true // Сигнал для JavaScript о необходимости перезагрузки
            ]);
        } else {
            $this->flashMessageService->error('Не удалось использовать зелье.');
            return response()->json([
                'success' => false,
                'reload' => true
            ]);
        }
    }
}
