<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'mine' => null,
    'index' => 0,
    'resources' => [],
    'possibleItems' => [],
    'canEnterMines' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'mine' => null,
    'index' => 0,
    'resources' => [],
    'possibleItems' => [],
    'canEnterMines' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>




    
    <?php if($mine->description ?? false): ?>
        <div class="mb-4 p-3 bg-[#3a3631] rounded-lg border border-[#514b3c]">
            <h4 class="text-[#e5b769] font-semibold mb-2 text-sm">Описание локации</h4>
            <p class="text-[#d3c6a6] text-xs leading-relaxed"><?php echo e($mine->description); ?></p>
        </div>
    <?php endif; ?>

    
    <?php if(isset($mine->players) && $mine->players->count() > 0): ?>
        <div class="mb-4 p-3 bg-[#3a3631] rounded-lg border border-[#514b3c]">
            <h4 class="text-[#e5b769] font-semibold mb-2 text-sm flex items-center">
                
                Игроки в локации (<?php echo e($mine->players->count()); ?>)
            </h4>
            <div class="grid grid-cols-2 gap-2">
                <?php $__currentLoopData = $mine->players->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $player): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center space-x-2 text-xs">
                        <span class="w-2 h-2 rounded-full <?php echo e($player->profile->race === 'solarius' ? 'bg-yellow-500' : 'bg-blue-500'); ?>"></span>
                        <span class="text-[#d3c6a6] truncate"><?php echo e($player->name); ?></span>
                        <span class="text-[#9a9483] text-xs">(<?php echo e($player->profile->level); ?>)</span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php if($mine->players->count() > 6): ?>
                    <div class="text-[#9a9483] text-xs col-span-2 text-center">
                        и еще <?php echo e($mine->players->count() - 6); ?> игроков...
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    
    <div class="mb-4">
        <h4 class="text-[#e5b769] font-semibold mb-3 text-sm flex items-center">
            
            Доступные ресурсы
        </h4>

        <?php if($resources && $resources->count() > 0): ?>
            <div id="resources-container-<?php echo e($index); ?>" class="resources-paginated-container">
                
                <div class="grid grid-cols-4 gap-3 mb-3" id="resources-grid-<?php echo e($index); ?>">
                    
                </div>

                
                <div id="resources-pagination-<?php echo e($index); ?>" class="flex justify-center items-center space-x-2">
                    
                </div>
            </div>

            
            <script type="application/json" id="resources-data-<?php echo e($index); ?>">
                <?php echo json_encode($resources->map(function($resource) {
                    return [
                        'id' => $resource->id,
                        'name' => $resource->name,
                        'icon' => $resource->icon_path ?? null,
                        'description' => $resource->description ?? ''
                    ];
                })->values()->toArray()); ?>

            </script>
        <?php else: ?>
            <div class="text-center py-4 bg-[#3a3631] rounded-lg border border-[#514b3c]">
                <span class="text-[#9a9483] text-xs">Нет доступных ресурсов</span>
            </div>
        <?php endif; ?>
    </div>

    
    <div class="mb-4">
        <h4 class="text-[#e5b769] font-semibold mb-3 text-sm flex items-center">
           
            Возможные награды
        </h4>

        <?php if($possibleItems && $possibleItems->count() > 0): ?>
            <div id="items-container-<?php echo e($index); ?>" class="items-paginated-container">
                
                <div class="grid grid-cols-4 gap-3 mb-3" id="items-grid-<?php echo e($index); ?>">
                    
                </div>

                
                <div id="items-pagination-<?php echo e($index); ?>" class="flex justify-center items-center space-x-2">
                    
                </div>
            </div>

            
            <script type="application/json" id="items-data-<?php echo e($index); ?>">
                <?php echo json_encode($possibleItems->map(function($itemData) {
                    return [
                        'id' => $itemData['item']->id,
                        'name' => $itemData['item']->name,
                        'icon' => $itemData['item']->icon_path ?? null,
                        'description' => $itemData['item']->description ?? '',
                        'drop_chance' => $itemData['drop_chance'],
                        'min_quantity' => $itemData['min_quantity'],
                        'max_quantity' => $itemData['max_quantity']
                    ];
                })->values()->toArray()); ?>

            </script>
        <?php else: ?>
            <div class="text-center py-4 bg-[#3a3631] rounded-lg border border-[#514b3c]">
                <span class="text-[#9a9483] text-xs">Нет доступных предметов</span>
            </div>
        <?php endif; ?>
    </div>

    
    <div class="mt-4 p-4 flex justify-center">
        <?php if(isset($canEnterMines) && $canEnterMines): ?>
            
            <button
                onclick="window.location='<?php echo e(route('battle.mines.custom.index', ['slug' => $mine->slug])); ?>'"
                class="w-full py-3 text-[#2f2d2b] font-bold uppercase tracking-wide
                    bg-gradient-to-b from-[#e5b769] to-[#c4a76d] rounded-lg border border-[#a6925e]
                    shadow-[inset_0px_4px_8px_rgba(0,0,0,0.5), inset_0px_-4px_8px_rgba(255,255,255,0.1)]
                    transition-all duration-200 relative
                    hover:scale-105 hover:shadow-[0px_4px_10px_rgba(255,215,105,0.5)]
                    active:scale-95 active:shadow-[inset_0px_4px_8px_rgba(0,0,0,0.6)]">
                <span class="relative z-10">Войти в рудник</span>
            </button>
        <?php else: ?>
            
            <button disabled
                class="w-full py-3 text-[#7a7666] font-bold uppercase tracking-wide
                    bg-gradient-to-b from-[#4a4a3d] to-[#3a3631] rounded-lg border border-[#514b3c]
                    shadow-[inset_0px_2px_4px_rgba(0,0,0,0.3)]
                    cursor-not-allowed opacity-60">
                <span class="relative z-10">Недостаточно здоровья</span>
            </button>
        <?php endif; ?>
    </div>
<?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/battle/mines/mine-resources-dropdown.blade.php ENDPATH**/ ?>