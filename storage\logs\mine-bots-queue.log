^C[32m⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)[39m
[32m==========================================[39m

[32m📤 Запуск обработки через очередь...[39m
[32m✅ Задача обработки всех рудников добавлена в очередь[39m

[32m💡 Для мониторинга очереди используйте:[39m
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots

   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()


   TypeError 

  Cannot assign null to property App\Services\Mine\MineBotCacheService::$ttlConfig of type array

  at app\Services\Mine\MineBotCacheService.php:28
     24▕     public function __construct(RedisKeyService $redisKeyService)
     25▕     {
     26▕         $this->config = config('mine_bots');
     27▕         $this->redisKeyService = $redisKeyService;
  ➜  28▕         $this->ttlConfig = config('cache_ttl');
     29▕     }
     30▕ 
     31▕     /**
     32▕      * Кэширует состояние бота

  1   [internal]:0
      App\Services\Mine\MineBotCacheService::__construct()

  2   vendor\laravel\framework\src\Illuminate\Container\Container.php:989
      ReflectionClass::newInstanceArgs()

⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
⛏️ НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA)
==========================================

📤 Запуск обработки через очередь...
✅ Задача обработки всех рудников добавлена в очередь

💡 Для мониторинга очереди используйте:
   php artisan queue:work --queue=mine_bots
