<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserPotion;
use App\Models\PotionBeltSlot;
use App\Services\FlashMessageService;

/**
 * Команда для тестирования флеш-сообщений при использовании зелий с пояса
 */
class TestPotionBeltFlashMessages extends Command
{
    protected $signature = 'potion-belt:test-flash-messages 
                            {user_id=10 : ID пользователя}';

    protected $description = 'Тестирование флеш-сообщений для использования зелий с пояса';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        $this->info("🧪 Тестирование флеш-сообщений для пояса зелий");
        $this->line("👤 Пользователь ID: {$userId}");
        
        // Находим пользователя
        $user = User::find($userId);
        if (!$user) {
            $this->error("❌ Пользователь с ID {$userId} не найден");
            return 1;
        }
        
        $this->line("✅ Пользователь найден: {$user->name}");
        
        // Проверяем зелья пользователя
        $userPotions = UserPotion::where('user_id', $userId)->get();
        $this->line("🧪 Зелий в инвентаре: {$userPotions->count()}");
        
        if ($userPotions->isEmpty()) {
            $this->error("❌ У пользователя нет зелий в инвентаре");
            return 1;
        }
        
        // Проверяем слоты пояса
        $beltSlots = PotionBeltSlot::where('user_id', $userId)->get();
        $this->line("🎒 Слотов на поясе: {$beltSlots->count()}");
        
        if ($beltSlots->isEmpty()) {
            $this->error("❌ У пользователя нет зелий на поясе");
            $this->line("💡 Добавьте зелья на пояс через инвентарь");
            return 1;
        }
        
        // Показываем информацию о зельях на поясе
        $this->line("\n📋 Зелья на поясе:");
        foreach ($beltSlots as $slot) {
            $potion = $slot->potion;
            if ($potion) {
                $this->line("   Слот {$slot->slot_position}: {$potion->name} (ID: {$potion->id})");
            }
        }
        
        // Тестируем флеш-сообщения
        $this->info("\n📨 Тестирование флеш-сообщений:");
        
        $flashService = app(FlashMessageService::class);
        
        // Тест 1: Сообщение об успешном использовании зелья
        $testPotion = $beltSlots->first()->potion;
        if ($testPotion) {
            $message = "Вы использовали {$testPotion->name}. Восстановлено 32 HP";
            
            // Получаем иконку зелья для отображения в сообщении
            $potionIcon = $testPotion->icon_path ?? null;
            if ($potionIcon) {
                $iconHtml = '<img src="' . asset($potionIcon) . '" alt="' . $testPotion->name . '" class="w-4 h-4 inline-block mr-1">';
                $flashService->success($message, $iconHtml);
                $this->line("✅ Flash-сообщение с иконкой добавлено: {$message}");
            } else {
                $flashService->success($message, '🧪');
                $this->line("✅ Flash-сообщение с эмодзи добавлено: {$message}");
            }
        }
        
        // Тест 2: Сообщение об ошибке
        $flashService->error('Не удалось использовать зелье.');
        $this->line("✅ Flash-сообщение об ошибке добавлено");
        
        // Тест 3: Сообщение о превышении лимита
        $flashService->warning('Слишком быстро! Подождите 3 сек.');
        $this->line("✅ Flash-сообщение с предупреждением добавлено");
        
        $this->line("\n🌐 Откройте любую страницу боя (например, /battle/outposts/elven-haven)");
        $this->line("   чтобы увидеть все типы флеш-сообщений");
        
        $this->line("\n📋 Добавленные сообщения:");
        $this->line("   • Успех: Использование зелья (с иконкой/эмодзи)");
        $this->line("   • Ошибка: Не удалось использовать зелье");
        $this->line("   • Предупреждение: Превышение лимита скорости");

        return 0;
    }
}
