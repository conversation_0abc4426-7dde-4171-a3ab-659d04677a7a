<?php $__env->startSection('content'); ?>
    
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    
    <div class="space-y-4">
        
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="text-2xl font-bold text-[#e5b769]">Банк</h1>
                <p class="text-sm text-[#a09a8a]">Хранение предметов и перевод валюты</p>
            </div>
            <img src="<?php echo e(asset('assets/bank.png')); ?>" alt="Банк" class="w-12 h-12 object-contain">
        </div>

        
        <div id="itemInfoModal"
            class="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center hidden p-4">
            <div
                class="bg-[#2f2d2b] border-2 border-[#a6925e] rounded-lg shadow-xl p-6 max-w-md mx-auto relative transform transition-all">
                
                <button type="button" id="closeItemInfoButton"
                    class="absolute top-2 right-2 text-[#a6925e] hover:text-[#e5b769] transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>

                
                <h3 id="itemInfoTitle"
                    class="text-xl font-bold text-[#e5b769] mb-4 text-center border-b border-[#514b3c] pb-2">
                    Информация о предмете
                </h3>

                
                <div id="itemInfoContent" class="max-h-[70vh] overflow-y-auto custom-scrollbar pr-2">
                    <div class="flex justify-center items-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#e5b769]"></div>
                        <span class="ml-2 text-[#d9d3b8]">Загрузка...</span>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="flex flex-wrap gap-1 border-b border-[#514b3c] pb-2">
            <a href="<?php echo e(route('bank.index', ['tab' => 'items'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'items' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Предметы
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('items')); ?>/<?php echo e($bankStorage->max_slots_items); ?>)</span>
            </a>
            <a href="<?php echo e(route('bank.index', ['tab' => 'resources'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'resources' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Ресурсы
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('resources')); ?>/<?php echo e($bankStorage->max_slots_resources); ?>)</span>
            </a>
            <a href="<?php echo e(route('bank.index', ['tab' => 'seeds'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'seeds' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Семена
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('seeds')); ?>/<?php echo e($bankStorage->max_slots_seeds); ?>)</span>
            </a>
            <a href="<?php echo e(route('bank.index', ['tab' => 'harvests'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'harvests' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Урожай
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('harvests')); ?>/<?php echo e($bankStorage->max_slots_harvests); ?>)</span>
            </a>
            <a href="<?php echo e(route('bank.index', ['tab' => 'ingredients'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'ingredients' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Ингредиенты
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('ingredients')); ?>/<?php echo e($bankStorage->max_slots_ingredients); ?>)</span>
            </a>
            <a href="<?php echo e(route('bank.index', ['tab' => 'recipes'])); ?>"
                class="px-3 py-1.5 rounded-t-md <?php echo e($tab === 'recipes' ? 'bg-[#38352c] text-[#e5b769] font-medium' : 'bg-[#2a2621] text-[#a09a8a] hover:bg-[#312e27]'); ?> transition-colors">
                Рецепты
                <span
                    class="text-xs opacity-70">(<?php echo e($bankStorage->getItemCount('recipes')); ?>/<?php echo e($bankStorage->max_slots_recipes); ?>)</span>
            </a>
        </div>

        
        <div class="bg-[#2a2621] p-3 rounded-md border border-[#514b3c] flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <div class="w-10 h-10 flex items-center justify-center bg-[#38352c] rounded-full border border-[#a6925e]">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#e5b769]" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                            clip-rule="evenodd" />
                    </svg>
                </div>
                <div>
                    <p class="text-[#a09a8a] text-sm">Свободно слотов:</p>
                    <p class="text-[#e5b769] font-medium"><?php echo e($bankStorage->getFreeSlots($tab)); ?> из
                        <?php echo e($bankStorage->{'max_slots_' . $tab}); ?>

                    </p>
                </div>
            </div>

            
            <div class="flex space-x-2">
                <a href="<?php echo e(route('bank.transactions.index')); ?>"
                    class="px-3 py-1.5 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#443f33] transition-colors text-sm">
                    Транзакции
                </a>
                <a href="<?php echo e(route('bank.transactions.create')); ?>"
                    class="px-3 py-1.5 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#443f33] transition-colors text-sm">
                    Новый перевод
                </a>
            </div>
        </div>

        
        <div class="bg-[#2a2621] p-4 rounded-md border border-[#514b3c]">
            <?php if($tab === 'items' && isset($items) && $items->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gameItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($gameItem->item->icon ?? 'assets/items/default.png')); ?>"
                                        alt="<?php echo e($gameItem->item->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <?php
                                        // Определяем цвет качества предмета
                                        $qualityColors = [
                                            'Обычное' => 'border-gray-400',
                                            'Необычное' => 'border-green-400',
                                            'Редкое' => 'border-blue-400',
                                            'Эпическое' => 'border-purple-400',
                                            'Легендарное' => 'border-orange-400',
                                        ];
                                        $borderColor = $qualityColors[$gameItem->item->quality] ?? 'border-gray-400';
                                    ?>
                                    <div class="absolute inset-0 border-2 <?php echo e($borderColor); ?> rounded opacity-60"></div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <a href="javascript:void(0)" onclick="showItemInfo(<?php echo e($gameItem->id); ?>)"
                                        class="text-[#e5b769] font-medium hover:text-[#f7d89b] hover:underline transition-colors truncate max-w-[180px]">
                                        <?php echo e($gameItem->item->name); ?>

                                    </a>
                                    <div class="flex flex-wrap items-center gap-1 text-xs text-[#a09a8a] mt-1">
                                        <span class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c]">
                                            <?php echo e($gameItem->item->type); ?>

                                        </span>
                                        <?php if($gameItem->durability < $gameItem->max_durability): ?>
                                            <span class="inline-block text-red-400">
                                                <?php echo e($gameItem->durability); ?>/<?php echo e($gameItem->max_durability); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="item_id" value="<?php echo e($gameItem->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($items->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php elseif($tab === 'resources' && isset($resources) && $resources->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($resource->resource->icon_path ?? 'assets/resources/default.png')); ?>"
                                        alt="<?php echo e($resource->resource->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <div
                                        class="absolute -bottom-1 -right-1 bg-[#2a2621] text-[#e5b769] text-xs font-bold px-1 rounded border border-[#514b3c]">
                                        <?php echo e($resource->quantity); ?>

                                    </div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <p class="text-[#e5b769] font-medium truncate max-w-[180px]">
                                        <?php echo e(explode(' ', $resource->resource->name)[0]); ?>

                                    </p>
                                    <?php if($resource->resource->type): ?>
                                        <span
                                            class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c] text-xs text-[#a09a8a]">
                                            <?php echo e($resource->resource->getTypeTranslation()); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST"
                                class="flex items-center space-x-1 flex-shrink-0">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="resource_id" value="<?php echo e($resource->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <input type="number" name="quantity" value="1" min="1" max="<?php echo e($resource->quantity); ?>"
                                    class="w-12 px-1 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] text-xs">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs whitespace-nowrap">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($resources->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php elseif($tab === 'seeds' && isset($seeds) && $seeds->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $seeds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $seed): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($seed->farmingSeed->icon_path ?? 'assets/seeds/default.png')); ?>"
                                        alt="<?php echo e($seed->farmingSeed->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <div
                                        class="absolute -bottom-1 -right-1 bg-[#2a2621] text-[#e5b769] text-xs font-bold px-1 rounded border border-[#514b3c]">
                                        <?php echo e($seed->quantity); ?>

                                    </div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <p class="text-[#e5b769] font-medium truncate max-w-[180px]"><?php echo e($seed->farmingSeed->name); ?></p>
                                    <?php if($seed->farmingSeed->quality): ?>
                                        <span
                                            class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c] text-xs text-[#a09a8a]">
                                            Качество: <?php echo e($seed->farmingSeed->quality); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST" class="flex items-center space-x-1">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="seed_id" value="<?php echo e($seed->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <input type="number" name="quantity" value="1" min="1" max="<?php echo e($seed->quantity); ?>"
                                    class="w-14 px-1 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] text-xs">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($seeds->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php elseif($tab === 'harvests' && isset($harvests) && $harvests->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $harvests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $harvest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($harvest->harvest->icon_path ?? 'assets/harvests/default.png')); ?>"
                                        alt="<?php echo e($harvest->harvest->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <div
                                        class="absolute -bottom-1 -right-1 bg-[#2a2621] text-[#e5b769] text-xs font-bold px-1 rounded border border-[#514b3c]">
                                        <?php echo e($harvest->quantity); ?>

                                    </div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <p class="text-[#e5b769] font-medium truncate max-w-[180px]"><?php echo e($harvest->harvest->name); ?></p>
                                    <?php if($harvest->plantType): ?>
                                        <span
                                            class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c] text-xs text-[#a09a8a]">
                                            <?php echo e($harvest->plantType->name); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST" class="flex items-center space-x-1">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="harvest_id" value="<?php echo e($harvest->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <input type="number" name="quantity" value="1" min="1" max="<?php echo e($harvest->quantity); ?>"
                                    class="w-14 px-1 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] text-xs">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($harvests->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php elseif($tab === 'ingredients' && isset($ingredients) && $ingredients->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $ingredients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ingredient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($ingredient->ingredient->icon_path ?? 'assets/ingredients/default.png')); ?>"
                                        alt="<?php echo e($ingredient->ingredient->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <div
                                        class="absolute -bottom-1 -right-1 bg-[#2a2621] text-[#e5b769] text-xs font-bold px-1 rounded border border-[#514b3c]">
                                        <?php echo e($ingredient->quantity); ?>

                                    </div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <p class="text-[#e5b769] font-medium truncate max-w-[180px]"><?php echo e($ingredient->ingredient->name); ?>

                                    </p>
                                    <?php if($ingredient->ingredient->rarity): ?>
                                        <span
                                            class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c] text-xs text-[#a09a8a]">
                                            Редкость: <?php echo e($ingredient->ingredient->rarity); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST" class="flex items-center space-x-1">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="ingredient_id" value="<?php echo e($ingredient->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <input type="number" name="quantity" value="1" min="1" max="<?php echo e($ingredient->quantity); ?>"
                                    class="w-14 px-1 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] text-xs">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($ingredients->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php elseif($tab === 'recipes' && isset($recipes) && $recipes->count() > 0): ?>
                
                <div class="space-y-3">
                    <?php $__currentLoopData = $recipes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recipe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div
                            class="flex items-center justify-between p-2 bg-[#38352c] rounded border border-[#514b3c] hover:border-[#a6925e] transition-colors">
                            <div class="flex items-center space-x-2">
                                <div class="relative w-10 h-10 flex-shrink-0">
                                    <img src="<?php echo e(asset($recipe->recipe->icon_path ?? 'assets/recipes/default.png')); ?>"
                                        alt="<?php echo e($recipe->recipe->name); ?>"
                                        class="w-9 h-9 object-contain border border-[#514b3c] rounded m-0.5">
                                    <div
                                        class="absolute -bottom-1 -right-1 bg-[#2a2621] text-[#e5b769] text-xs font-bold px-1 rounded border border-[#514b3c]">
                                        <?php echo e($recipe->quantity); ?>

                                    </div>
                                </div>
                                <div class="flex flex-col justify-center min-w-0">
                                    <p class="text-[#e5b769] font-medium truncate max-w-[180px]"><?php echo e($recipe->recipe->name); ?></p>
                                    <?php if($recipe->recipe->difficulty): ?>
                                        <span
                                            class="inline-block px-1.5 py-0.5 bg-[#2a2621] rounded border border-[#514b3c] text-xs text-[#a09a8a]">
                                            Сложность: <?php echo e($recipe->recipe->difficulty); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="<?php echo e(route('bank.move-to-inventory')); ?>" method="POST" class="flex items-center space-x-1">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="recipe_id" value="<?php echo e($recipe->id); ?>">
                                <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                                <input type="number" name="quantity" value="1" min="1" max="<?php echo e($recipe->quantity); ?>"
                                    class="w-14 px-1 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] text-xs">
                                <button type="submit"
                                    class="px-2 py-1 bg-[#2a2621] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#38352c] transition-colors text-xs">
                                    Рюкзак
                                </button>
                            </form>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                
                <div class="mt-4">
                    <?php echo e($recipes->appends(['tab' => $tab])->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <p class="text-[#a09a8a]">В банке нет предметов данного типа</p>
                </div>
            <?php endif; ?>
        </div>

        
        <?php if(isset($storageUpgrades)): ?>
            <div class="bg-[#2a2621] p-4 rounded-md border border-[#514b3c]">
                <h3 class="text-[#e5b769] font-medium mb-2">Улучшение хранилища</h3>
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-[#a09a8a] text-sm">Текущая вместимость: <?php echo e($bankStorage->{"max_slots_$tab"}); ?> слотов
                        </p>
                        <p class="text-[#a09a8a] text-sm">Стоимость улучшения:</p>
                        <div class="flex items-center mt-1">
                            <?php echo $storageUpgrades->getPriceHtml(); ?>

                        </div>
                    </div>
                    <form action="<?php echo e(route('bank.upgrade-storage')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="storage_type" value="<?php echo e($tab); ?>">
                        <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
                        <button type="submit"
                            class="px-3 py-1.5 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#443f33] transition-colors">
                            Улучшить (+1 слот)
                        </button>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    
    <script>
        // Функция для отображения информации о предмете
        function showItemInfo(itemId) {
            // Получаем CSRF-токен
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Показываем модальное окно с индикатором загрузки
            const modal = document.getElementById('itemInfoModal');
            const content = document.getElementById('itemInfoContent');

            // Отображаем модальное окно
            modal.classList.remove('hidden');

            // Загружаем данные о предмете через AJAX
            fetch(`/inventory/item-info/${itemId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Обновляем заголовок модального окна
                        document.getElementById('itemInfoTitle').textContent = data.item.name;

                        // Заполняем контент модального окна
                        content.innerHTML = data.html;
                    } else {
                        // Показываем сообщение об ошибке
                        content.innerHTML = `<div class="text-center py-4">
                                                                                                        <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                                                                                                        <div class="text-[#d9d3b8] text-sm">${data.message || 'Не удалось загрузить данные.'}</div>
                                                                                                        <button onclick="document.getElementById('itemInfoModal').classList.add('hidden')" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                                                                                            Закрыть
                                                                                                        </button>
                                                                                                    </div>`;
                    }
                })
                .catch(error => {
                    // Показываем сообщение об ошибке
                    content.innerHTML = `<div class="text-center py-4">
                                                                                                    <div class="text-red-500 text-lg mb-2">Произошла ошибка при загрузке информации о предмете.</div>
                                                                                                    <div class="text-[#d9d3b8] text-sm">Ошибка сети или сервера.</div>
                                                                                                    <button onclick="document.getElementById('itemInfoModal').classList.add('hidden')" class="mt-4 px-4 py-2 bg-[#38352c] text-[#e5b769] rounded border border-[#514b3c] hover:bg-[#4a452c]">
                                                                                                        Закрыть
                                                                                                    </button>
                                                                                                </div>`;
                    console.error('Ошибка:', error);
                });
        }

        // Закрытие модального окна при клике на кнопку закрытия
        document.getElementById('closeItemInfoButton').addEventListener('click', function () {
            document.getElementById('itemInfoModal').classList.add('hidden');
        });

        // Закрытие модального окна при клике вне его содержимого
        document.getElementById('itemInfoModal').addEventListener('click', function (e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('sample', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/bank/index.blade.php ENDPATH**/ ?>